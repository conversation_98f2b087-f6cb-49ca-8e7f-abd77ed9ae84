import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

const initialCurrentConversation = {
  id: null,
  type: null,
  name: null
};

const initialState = {
  currentConversation: { ...initialCurrentConversation },
  takeoverId: null,
};

export const useConversationStore = create(
  persist(
    (set, get) => ({
      ...initialState,
      setCurrentConversation: (conversation) => set({ currentConversation: conversation }),
      updateCurrentConversationName: (name) => set((state) => ({
        currentConversation: { ...state.currentConversation, name }
      })),
      setTakeoverId: (id) => set({ takeoverId: id }),
      resetCurrentConversation: () =>
        set({ currentConversation: { ...initialCurrentConversation } }),
      reset: () => set({ ...initialState })
    }),
    {
      name: 'conversation-store',
      storage: createJSONStorage(() => localStorage)
    }
  )
);
