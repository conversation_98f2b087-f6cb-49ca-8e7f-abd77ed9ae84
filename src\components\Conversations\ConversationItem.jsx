import { useState, useEffect } from 'react';
import clsx from 'clsx';

import { useConversationStore } from '@/stores/conversation/conversationStore';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/react';

import DButton from '../Global/DButton';
import DButtonIcon from '../Global/DButtonIcon';
import DInput from '../Global/DInput/DInput';
import CheckmarkIcon from '../Global/Icons/CheckmarkIcon';
import CloseIcon from '../Global/Icons/CloseIcon';
import DeleteIcon from '../Global/Icons/DeleteIcon';
import EditIcon from '../Global/Icons/EditIcon';
import OptionsIcon from '../Global/Icons/OptionsIcon';
import ShareIcon from '../Global/Icons/ShareIcon';
import useTeamManagementStore from '@/stores/teamManagement/teamManagementStore';
import { useUserStore } from '@/stores/user/userStore';
import featureCheck from '@/helpers/tier/featureCheck';

const ConversationItem = ({
  conversation,
  handleRename,
  handleDelete,
  handleShare,
  handleOpenConversation,
}) => {
  const currentConversationId = useConversationStore(
    (state) => state.currentConversation.id
  );
  const selectedTeam = useTeamManagementStore(
    (state) => state.selectedTeam
  );

  const userData = useUserStore(
    (state) => state.user
  );
  const [isRenaming, setIsRenaming] = useState(false);

  const [name, setName] = useState(conversation.name || 'Untitled');

  // Update local name state when conversation prop changes
  useEffect(() => {
    setName(conversation.name || 'Untitled');
  }, [conversation.name]);

  return (
    <article 
    className={clsx(
      'flex flex-col border-l border-white justify-between py-size0 pr-size1 hover:bg-grey-2 hover:border-grey-20',
      isRenaming && 'bg-grey-2 !border-grey-20',
      currentConversationId === conversation.id && 'bg-grey-2 !border-grey-20',
    )}
    >
      <div
        className='flex items-center gap-size1'
      >
        {!isRenaming && (
          <>
            <button
              className={clsx(
                'text-sm pl-size1 py-size1 text-left w-[calc(100%-32px)]',
                selectedTeam && selectedTeam.owner_id === userData.id && 'pb-size0'
              )}
              onClick={() => handleOpenConversation(conversation.id)}
            >
              {conversation.name || 'Untitled'}
            </button>

            <Menu>
              <MenuButton className="inline-flex items-center justify-center gap-2 rounded-size1   font-semibold text-black  focus:outline-none data-[hover]:bg-grey-5 data-[open]:bg-grey-5 data-[focus]:outline-1 data-[focus]:outline-black size-8">
                <OptionsIcon />
              </MenuButton>

              <MenuItems
                transition
                anchor="bottom end"
                className="w-52 origin-top-right rounded-size1 border border-black/5 bg-grey-100 p-1  text-black transition duration-100 ease-out [--anchor-gap:var(--spacing-1)] focus:outline-none data-[closed]:scale-95 data-[closed]:opacity-0"
              >
                {featureCheck('share_conversations') && (<MenuItem>
                  <button
                    className="flex items-center gap-size1 py-size2 px-size1 text-xs font-regular tracking-tight hover:bg-grey-2 dark:hover:bg-[#000]/30 dark:text-white rounded-size1 w-full "
                    onClick={() => {
                      handleShare({ id: conversation.id });
                    }}
                  >
                    <ShareIcon className="dark:text-white" />
                    <span>Share</span>
                  </button>
                </MenuItem>)}
                <MenuItem>
                  <button
                    className="flex items-center gap-size1 py-size2 px-size1 text-xs font-regular tracking-tight hover:bg-grey-2 dark:hover:bg-[#000]/30 dark:text-white rounded-size1 w-full "
                    onClick={() => {
                      setIsRenaming(true);
                    }}
                  >
                    <EditIcon className="dark:text-white" />
                    <span>Rename</span>
                  </button>
                </MenuItem>
                <MenuItem>
                  <button
                    className="flex items-center gap-size1 text-error py-size2 px-size1 text-xs font-regular tracking-tight hover:bg-grey-2 rounded-size1 w-full "
                    onClick={() => {
                      handleDelete({ id: conversation.id });
                    }}
                  >
                    <DeleteIcon />
                    <span>Delete</span>
                  </button>
                </MenuItem>
              </MenuItems>
            </Menu>
          </>
        )}
        {isRenaming && (
          <>
            <DInput
              value={name}
              onChange={({ target }) => setName(target.value)}
              size="sm"
              className="!p-0 !h-9 !px-size1 border-none"
            />
            <DButtonIcon
              onClick={() => {
                setIsRenaming(false);
                handleRename({ id: conversation.id, name });
              }}
              variant="grey"
              className="size-8"
            >
              <CheckmarkIcon />
            </DButtonIcon>
            <DButtonIcon
              onClick={() => {
                setIsRenaming(false);
                setName(conversation.name);
              }}
              variant="grey"
              className="size-8"
            >
              <CloseIcon />
            </DButtonIcon>
          </>
        )}
      </div>
        {!isRenaming && selectedTeam && (
          <div className='flex items-center gap-size1 pl-size1'>
            <span className='text-xs font-regular text-grey-50'>{selectedTeam.owner_id === userData.id && conversation.created_by}</span>
          </div>
        )}
    </article>
  );
};

export default ConversationItem;
